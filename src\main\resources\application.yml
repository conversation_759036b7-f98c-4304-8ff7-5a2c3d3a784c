server:
  port: 5000

spring:
  application:
    name: video-player

  # 数据库配置
  datasource:
    url: ******************************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect

  # Thymeleaf配置
  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html
    cache: false
    encoding: UTF-8
    mode: HTML

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
# 日志配置
logging:
  level:
    com.videoplayer: DEBUG
    com.videoplayer.exception.GlobalExceptionHandler: ERROR
    org.springframework.web.*: off
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
