# 简化版视频播放器

一个简洁的Spring Boot视频播放器项目，专注于核心功能。

## 🎯 项目特点

- **极简设计**: 删除了所有复杂的监控、缓存、性能分析等功能
- **核心功能**: 专注于视频的上传、播放、管理
- **易于部署**: 简化的Docker配置，只需要应用和数据库
- **轻量级**: 最少的依赖和配置

## 🏗️ 技术栈

- **后端**: Spring Boot 3.2.0 + Java 17
- **数据库**: MySQL 8.0 + JPA
- **前端**: Thymeleaf + Bootstrap 5
- **容器化**: Docker + Docker Compose

## 📁 项目结构

```
src/
├── main/
│   ├── java/com/videoplayer/
│   │   ├── VideoPlayerApplication.java     # 主启动类
│   │   ├── controller/
│   │   │   ├── VideoApi.java              # 视频API控制器
│   │   │   └── PageController.java        # 页面控制器
│   │   ├── entity/
│   │   │   └── Video.java                 # 视频实体
│   │   ├── repository/
│   │   │   └── VideoRepository.java       # 视频数据访问
│   │   ├── service/
│   │   │   └── VideoService.java          # 视频业务逻辑
│   │   ├── config/
│   │   │   ├── SecurityConfig.java        # 安全配置
│   │   │   └── WebConfig.java             # Web配置
│   │   ├── common/
│   │   │   └── ApiResponse.java           # 统一响应格式
│   │   └── exception/
│   │       ├── GlobalExceptionHandler.java
│   │       ├── BusinessException.java
│   │       └── ResourceNotFoundException.java
│   ├── resources/
│   │   ├── application.yml                # 应用配置
│   │   ├── templates/                     # 页面模板
│   │   │   ├── index.html                # 首页
│   │   │   ├── videos.html               # 视频列表
│   │   │   ├── play.html                 # 播放页面
│   │   │   ├── admin.html                # 管理页面
│   │   │   └── error.html                # 错误页面
│   │   └── static/                       # 静态资源
│   │       ├── css/                      # 样式文件
│   │       ├── js/                       # JavaScript文件
│   │       └── images/                   # 图片资源
└── test/                                 # 测试代码
```

## 🚀 核心功能

### 视频管理
- ✅ 视频上传和存储
- ✅ 视频信息编辑
- ✅ 视频删除（软删除）
- ✅ 视频列表展示
- ✅ 视频搜索功能

### 视频播放
- ✅ 基本视频播放功能
- ✅ 支持多种视频格式
- ✅ 响应式播放器界面

### 管理功能
- ✅ 简单的管理后台
- ✅ 视频状态管理
- ✅ 基本的用户界面

## 📊 API接口

### 视频相关API
- `GET /api/videos` - 获取视频列表（分页）
- `GET /api/videos/{id}` - 获取视频详情
- `GET /api/videos/search` - 搜索视频
- `POST /api/videos` - 添加视频
- `PUT /api/videos/{id}` - 更新视频
- `DELETE /api/videos/{id}` - 删除视频

### 页面路由
- `/` - 首页
- `/videos` - 视频列表页
- `/play/{id}` - 视频播放页
- `/admin` - 管理页面

## 🐳 快速部署

### 使用Docker Compose
```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 本地开发
```bash
# 启动MySQL数据库
# 配置数据库连接信息在 application.yml

# 运行应用
mvn spring-boot:run
```

## 🔧 配置说明

### 数据库配置
```yaml
spring:
  datasource:
    url: ****************************************
    username: root
    password: root
```

### 文件上传配置
```yaml
spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
```

## 📝 数据库表结构

### videos表
- `id` - 主键
- `title` - 视频标题
- `description` - 视频描述
- `video_url` - 视频URL
- `thumbnail_url` - 缩略图URL
- `duration` - 视频时长
- `file_size` - 文件大小
- `video_format` - 视频格式
- `resolution` - 分辨率
- `play_count` - 播放次数
- `like_count` - 点赞数
- `category_id` - 分类ID
- `tags` - 标签
- `status` - 状态
- `is_active` - 是否启用
- `created_time` - 创建时间
- `updated_time` - 更新时间
- `created_by` - 创建者
- `updated_by` - 更新者

## 🎉 简化说明

相比原版本，此简化版删除了以下复杂功能：
- ❌ 性能监控和指标收集
- ❌ 复杂的缓存系统
- ❌ 健康检查和监控
- ❌ 非核心实体（分类、播放日志、联系表单）
- ❌ 复杂的配置类和属性
- ❌ Docker监控服务（Prometheus、Grafana）
- ❌ 复杂的前端功能和优化
- ❌ 网络监控和自适应质量控制
- ❌ 分享功能和质量指示器

## 📞 支持

如需帮助，请查看代码注释或提交Issue。

---

**专注核心，简单高效！**
