package com.videoplayer.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.SecurityFilterChain;

/**
 * 安全配置类
 * 兼容Spring Security 6.x
 *
 * <AUTHOR>
 * @version 2.0.0
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF（因为是API应用）
            .csrf(AbstractHttpConfigurer::disable)
            
            // 配置授权规则
            .authorizeHttpRequests(authz -> authz
                // 允许访问所有资源（开发环境）
                .anyRequest().permitAll()
            )
            
            // 基础安全头配置
            .headers(headers -> headers
                // 防止点击劫持
                .frameOptions(frameOptions -> frameOptions.sameOrigin())
                // 内容类型嗅探保护
                .contentTypeOptions(contentTypeOptions -> {})
            );

        return http.build();
    }
}
