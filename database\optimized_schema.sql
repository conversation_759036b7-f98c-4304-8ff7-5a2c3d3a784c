-- 优化后的视频播放器数据库结构
-- 数据库版本: MySQL 8.0+
-- 字符集: UTF-8
-- 优化内容: 索引优化、性能优化、数据完整性约束

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `video_player` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `video_player`;

-- 优化后的视频表
CREATE TABLE IF NOT EXISTS `videos` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '视频ID',
    `title` VARCHAR(200) NOT NULL COMMENT '视频标题',
    `description` TEXT DEFAULT NULL COMMENT '视频描述',
    `video_url` VARCHAR(1000) NOT NULL COMMENT '视频URL地址',
    `thumbnail_url` VARCHAR(1000) DEFAULT NULL COMMENT '缩略图URL',
    `duration` INT UNSIGNED DEFAULT NULL COMMENT '视频时长（秒）',
    `file_size` BIGINT UNSIGNED DEFAULT NULL COMMENT '文件大小（字节）',
    `video_format` VARCHAR(20) DEFAULT NULL COMMENT '视频格式',
    `resolution` VARCHAR(20) DEFAULT NULL COMMENT '分辨率',
    `play_count` BIGINT UNSIGNED DEFAULT 0 COMMENT '播放次数',
    `like_count` INT UNSIGNED DEFAULT 0 COMMENT '点赞数',
    `category_id` BIGINT DEFAULT NULL COMMENT '分类ID',
    `tags` JSON DEFAULT NULL COMMENT '标签（JSON格式）',
    `status` ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') DEFAULT 'PUBLISHED' COMMENT '状态',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` VARCHAR(100) DEFAULT NULL COMMENT '创建者',
    `updated_by` VARCHAR(100) DEFAULT NULL COMMENT '更新者',
    
    -- 索引优化
    INDEX `idx_title` (`title`),
    INDEX `idx_status_active` (`status`, `is_active`),
    INDEX `idx_created_time` (`created_time` DESC),
    INDEX `idx_play_count` (`play_count` DESC),
    INDEX `idx_category` (`category_id`),
    INDEX `idx_duration` (`duration`),
    INDEX `idx_video_format` (`video_format`),
    INDEX `idx_resolution` (`resolution`),
    
    -- 全文索引用于搜索
    FULLTEXT INDEX `ft_title_description` (`title`, `description`),
    
    -- 复合索引优化查询性能
    INDEX `idx_status_active_created` (`status`, `is_active`, `created_time` DESC),
    INDEX `idx_active_play_count` (`is_active`, `play_count` DESC),
    
    -- 外键约束
    FOREIGN KEY (`category_id`) REFERENCES `video_categories`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频信息表';

-- 视频分类表
CREATE TABLE IF NOT EXISTS `video_categories` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID',
    `name` VARCHAR(100) NOT NULL COMMENT '分类名称',
    `description` VARCHAR(500) DEFAULT NULL COMMENT '分类描述',
    `parent_id` BIGINT DEFAULT NULL COMMENT '父分类ID',
    `sort_order` INT DEFAULT 0 COMMENT '排序顺序',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX `idx_name` (`name`),
    INDEX `idx_parent_id` (`parent_id`),
    INDEX `idx_sort_order` (`sort_order`),
    INDEX `idx_is_active` (`is_active`),
    
    FOREIGN KEY (`parent_id`) REFERENCES `video_categories`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频分类表';

-- 优化后的联系信息表
CREATE TABLE IF NOT EXISTS `lead_contacts` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '联系信息ID',
    `contact_name` VARCHAR(100) NOT NULL COMMENT '联系人姓名',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '电话号码',
    `wechat` VARCHAR(100) DEFAULT NULL COMMENT '微信号',
    `douyin` VARCHAR(100) DEFAULT NULL COMMENT '抖音号',
    `douyin_nickname` VARCHAR(100) DEFAULT NULL COMMENT '抖音昵称',
    `contact_type` ENUM('MANAGER', 'CUSTOMER_SERVICE', 'SALES') DEFAULT 'CUSTOMER_SERVICE' COMMENT '联系类型',
    `is_default` BOOLEAN DEFAULT FALSE COMMENT '是否为默认联系方式',
    `sort_order` INT DEFAULT 0 COMMENT '排序顺序',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_contact_name` (`contact_name`),
    INDEX `idx_phone` (`phone`),
    INDEX `idx_contact_type` (`contact_type`),
    INDEX `idx_is_default` (`is_default`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_sort_order` (`sort_order`),
    
    -- 复合索引
    INDEX `idx_active_sort` (`is_active`, `sort_order`),
    
    -- 唯一约束
    UNIQUE KEY `uk_phone` (`phone`),
    UNIQUE KEY `uk_wechat` (`wechat`),
    UNIQUE KEY `uk_douyin` (`douyin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联系信息表';

-- 播放记录表（用于统计分析）
CREATE TABLE IF NOT EXISTS `video_play_logs` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '播放记录ID',
    `video_id` BIGINT NOT NULL COMMENT '视频ID',
    `user_ip` VARCHAR(45) DEFAULT NULL COMMENT '用户IP',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
    `play_duration` INT UNSIGNED DEFAULT NULL COMMENT '播放时长（秒）',
    `play_progress` DECIMAL(5,2) DEFAULT NULL COMMENT '播放进度（百分比）',
    `device_type` ENUM('DESKTOP', 'MOBILE', 'TABLET') DEFAULT NULL COMMENT '设备类型',
    `browser_type` VARCHAR(50) DEFAULT NULL COMMENT '浏览器类型',
    `referrer` VARCHAR(500) DEFAULT NULL COMMENT '来源页面',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '播放时间',
    
    INDEX `idx_video_id` (`video_id`),
    INDEX `idx_user_ip` (`user_ip`),
    INDEX `idx_created_time` (`created_time` DESC),
    INDEX `idx_device_type` (`device_type`),
    
    -- 复合索引用于统计分析
    INDEX `idx_video_created` (`video_id`, `created_time` DESC),
    
    FOREIGN KEY (`video_id`) REFERENCES `videos`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频播放记录表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS `system_configs` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `config_value` TEXT DEFAULT NULL COMMENT '配置值',
    `config_type` ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON') DEFAULT 'STRING' COMMENT '配置类型',
    `description` VARCHAR(500) DEFAULT NULL COMMENT '配置描述',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY `uk_config_key` (`config_key`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入默认分类数据
INSERT INTO `video_categories` (`name`, `description`, `sort_order`) VALUES
('产品介绍', '产品相关的介绍视频', 1),
('使用教程', '产品使用方法和教程', 2),
('效果展示', '产品效果展示和用户反馈', 3),
('健康知识', '健康生活相关知识分享', 4);

-- 插入默认联系信息数据
INSERT INTO `lead_contacts` (`contact_name`, `phone`, `wechat`, `douyin`, `douyin_nickname`, `contact_type`, `is_default`, `sort_order`) VALUES
('林佳', '18722880704', '18722880704', '黄林佳', '皮皮管理', 'MANAGER', TRUE, 1),
('黄超', '18057722960', '18057722960', '黄超(黄小燕弟弟)', '黄超', 'MANAGER', FALSE, 2),
('小班', '15908542510', '15908542510', '佳茵轻康SOS小班', '小班', 'CUSTOMER_SERVICE', FALSE, 3);

-- 插入系统配置数据
INSERT INTO `system_configs` (`config_key`, `config_value`, `config_type`, `description`) VALUES
('site_title', '佳茵轻康视频播放器', 'STRING', '网站标题'),
('max_video_size', '104857600', 'NUMBER', '最大视频文件大小（字节）'),
('enable_play_statistics', 'true', 'BOOLEAN', '是否启用播放统计'),
('default_video_quality', '720p', 'STRING', '默认视频质量'),
('oss_config', '{"endpoint":"https://oss-cn-guangzhou.aliyuncs.com","bucket":"jyqk"}', 'JSON', '阿里云OSS配置');

-- 创建视图：热门视频
CREATE OR REPLACE VIEW `v_popular_videos` AS
SELECT 
    v.*,
    vc.name as category_name
FROM `videos` v
LEFT JOIN `video_categories` vc ON v.category_id = vc.id
WHERE v.is_active = TRUE AND v.status = 'PUBLISHED'
ORDER BY v.play_count DESC, v.created_time DESC;

-- 创建视图：最新视频
CREATE OR REPLACE VIEW `v_latest_videos` AS
SELECT 
    v.*,
    vc.name as category_name
FROM `videos` v
LEFT JOIN `video_categories` vc ON v.category_id = vc.id
WHERE v.is_active = TRUE AND v.status = 'PUBLISHED'
ORDER BY v.created_time DESC;

-- 数据库初始化完成
SELECT '优化后的数据库初始化完成！' as message;
SELECT COUNT(*) as video_count FROM videos;
SELECT COUNT(*) as contact_count FROM lead_contacts;
SELECT COUNT(*) as category_count FROM video_categories;
SELECT COUNT(*) as config_count FROM system_configs;
