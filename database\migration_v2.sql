-- 数据库迁移脚本 v1.0 -> v2.0
-- 用于将现有数据库升级到优化版本

USE `video_player`;

-- 1. 备份现有数据
CREATE TABLE IF NOT EXISTS `videos_backup` AS SELECT * FROM `videos`;
CREATE TABLE IF NOT EXISTS `lead_contacts_backup` AS SELECT * FROM `lead_contacts`;

-- 2. 添加新字段到videos表
ALTER TABLE `videos` 
ADD COLUMN `play_count` BIGINT UNSIGNED DEFAULT 0 COMMENT '播放次数' AFTER `resolution`,
ADD COLUMN `like_count` INT UNSIGNED DEFAULT 0 COMMENT '点赞数' AFTER `play_count`,
ADD COLUMN `category_id` BIGINT DEFAULT NULL COMMENT '分类ID' AFTER `like_count`,
ADD COLUMN `tags` JSON DEFAULT NULL COMMENT '标签（JSON格式）' AFTER `category_id`,
ADD COLUMN `status` ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') DEFAULT 'PUBLISHED' COMMENT '状态' AFTER `tags`,
ADD COLUMN `created_by` VARCHAR(100) DEFAULT NULL COMMENT '创建者' AFTER `updated_time`,
ADD COLUMN `updated_by` VARCHAR(100) DEFAULT NULL COMMENT '更新者' AFTER `created_by`;

-- 3. 修改description字段类型
ALTER TABLE `videos` MODIFY COLUMN `description` TEXT DEFAULT NULL COMMENT '视频描述';

-- 4. 修改数值字段为无符号类型
ALTER TABLE `videos` 
MODIFY COLUMN `duration` INT UNSIGNED DEFAULT NULL COMMENT '视频时长（秒）',
MODIFY COLUMN `file_size` BIGINT UNSIGNED DEFAULT NULL COMMENT '文件大小（字节）';

-- 5. 创建视频分类表
CREATE TABLE IF NOT EXISTS `video_categories` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID',
    `name` VARCHAR(100) NOT NULL COMMENT '分类名称',
    `description` VARCHAR(500) DEFAULT NULL COMMENT '分类描述',
    `parent_id` BIGINT DEFAULT NULL COMMENT '父分类ID',
    `sort_order` INT DEFAULT 0 COMMENT '排序顺序',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX `idx_name` (`name`),
    INDEX `idx_parent_id` (`parent_id`),
    INDEX `idx_sort_order` (`sort_order`),
    INDEX `idx_is_active` (`is_active`),
    
    FOREIGN KEY (`parent_id`) REFERENCES `video_categories`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频分类表';

-- 6. 优化lead_contacts表
ALTER TABLE `lead_contacts`
MODIFY COLUMN `contact_name` VARCHAR(100) NOT NULL COMMENT '联系人姓名',
MODIFY COLUMN `contact_type` ENUM('MANAGER', 'CUSTOMER_SERVICE', 'SALES') DEFAULT 'CUSTOMER_SERVICE' COMMENT '联系类型';

-- 7. 添加唯一约束到lead_contacts表
ALTER TABLE `lead_contacts`
ADD CONSTRAINT `uk_phone` UNIQUE (`phone`),
ADD CONSTRAINT `uk_wechat` UNIQUE (`wechat`),
ADD CONSTRAINT `uk_douyin` UNIQUE (`douyin`);

-- 8. 创建播放记录表
CREATE TABLE IF NOT EXISTS `video_play_logs` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '播放记录ID',
    `video_id` BIGINT NOT NULL COMMENT '视频ID',
    `user_ip` VARCHAR(45) DEFAULT NULL COMMENT '用户IP',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
    `play_duration` INT UNSIGNED DEFAULT NULL COMMENT '播放时长（秒）',
    `play_progress` DECIMAL(5,2) DEFAULT NULL COMMENT '播放进度（百分比）',
    `device_type` ENUM('DESKTOP', 'MOBILE', 'TABLET') DEFAULT NULL COMMENT '设备类型',
    `browser_type` VARCHAR(50) DEFAULT NULL COMMENT '浏览器类型',
    `referrer` VARCHAR(500) DEFAULT NULL COMMENT '来源页面',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '播放时间',
    
    INDEX `idx_video_id` (`video_id`),
    INDEX `idx_user_ip` (`user_ip`),
    INDEX `idx_created_time` (`created_time` DESC),
    INDEX `idx_device_type` (`device_type`),
    INDEX `idx_video_created` (`video_id`, `created_time` DESC),
    
    FOREIGN KEY (`video_id`) REFERENCES `videos`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频播放记录表';

-- 9. 创建系统配置表
CREATE TABLE IF NOT EXISTS `system_configs` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `config_value` TEXT DEFAULT NULL COMMENT '配置值',
    `config_type` ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON') DEFAULT 'STRING' COMMENT '配置类型',
    `description` VARCHAR(500) DEFAULT NULL COMMENT '配置描述',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY `uk_config_key` (`config_key`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 10. 添加新的索引
ALTER TABLE `videos`
ADD INDEX `idx_status_active` (`status`, `is_active`),
ADD INDEX `idx_play_count` (`play_count` DESC),
ADD INDEX `idx_category` (`category_id`),
ADD INDEX `idx_video_format` (`video_format`),
ADD INDEX `idx_resolution` (`resolution`),
ADD INDEX `idx_status_active_created` (`status`, `is_active`, `created_time` DESC),
ADD INDEX `idx_active_play_count` (`is_active`, `play_count` DESC);

-- 11. 添加全文索引
ALTER TABLE `videos`
ADD FULLTEXT INDEX `ft_title_description` (`title`, `description`);

-- 12. 添加外键约束
ALTER TABLE `videos`
ADD CONSTRAINT `fk_videos_category` FOREIGN KEY (`category_id`) REFERENCES `video_categories`(`id`) ON DELETE SET NULL;

-- 13. 优化lead_contacts表索引
ALTER TABLE `lead_contacts`
ADD INDEX `idx_contact_type` (`contact_type`),
ADD INDEX `idx_active_sort` (`is_active`, `sort_order`);

-- 14. 插入默认分类数据
INSERT IGNORE INTO `video_categories` (`name`, `description`, `sort_order`) VALUES
('产品介绍', '产品相关的介绍视频', 1),
('使用教程', '产品使用方法和教程', 2),
('效果展示', '产品效果展示和用户反馈', 3),
('健康知识', '健康生活相关知识分享', 4);

-- 15. 更新现有联系信息的contact_type
UPDATE `lead_contacts` SET `contact_type` = 'MANAGER' WHERE `contact_type` = '主管';
UPDATE `lead_contacts` SET `contact_type` = 'CUSTOMER_SERVICE' WHERE `contact_type` = '客服';

-- 16. 插入系统配置数据
INSERT IGNORE INTO `system_configs` (`config_key`, `config_value`, `config_type`, `description`) VALUES
('site_title', '佳茵轻康视频播放器', 'STRING', '网站标题'),
('max_video_size', '104857600', 'NUMBER', '最大视频文件大小（字节）'),
('enable_play_statistics', 'true', 'BOOLEAN', '是否启用播放统计'),
('default_video_quality', '720p', 'STRING', '默认视频质量'),
('oss_config', '{"endpoint":"https://oss-cn-guangzhou.aliyuncs.com","bucket":"jyqk"}', 'JSON', '阿里云OSS配置');

-- 17. 创建视图
CREATE OR REPLACE VIEW `v_popular_videos` AS
SELECT 
    v.*,
    vc.name as category_name
FROM `videos` v
LEFT JOIN `video_categories` vc ON v.category_id = vc.id
WHERE v.is_active = TRUE AND v.status = 'PUBLISHED'
ORDER BY v.play_count DESC, v.created_time DESC;

CREATE OR REPLACE VIEW `v_latest_videos` AS
SELECT 
    v.*,
    vc.name as category_name
FROM `videos` v
LEFT JOIN `video_categories` vc ON v.category_id = vc.id
WHERE v.is_active = TRUE AND v.status = 'PUBLISHED'
ORDER BY v.created_time DESC;

-- 18. 更新现有视频状态
UPDATE `videos` SET `status` = 'PUBLISHED' WHERE `status` IS NULL;

-- 迁移完成
SELECT '数据库迁移完成！' as message;
SELECT 'v2.0' as version;
SELECT COUNT(*) as video_count FROM videos;
SELECT COUNT(*) as contact_count FROM lead_contacts;
SELECT COUNT(*) as category_count FROM video_categories;
SELECT COUNT(*) as config_count FROM system_configs;
