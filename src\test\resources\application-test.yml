# 测试环境配置
spring:
  # 使用MySQL数据库进行测试
  datasource:
    url: ***********************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: root
    hikari:
      maximum-pool-size: 5
      minimum-idle: 1
      connection-timeout: 10000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false
  
  # 禁用Redis缓存
  cache:
    type: none
  
  # 禁用Redis连接
  data:
    redis:
      repositories:
        enabled: false
  
  # 模板引擎配置
  thymeleaf:
    cache: false

# 日志配置
logging:
  level:
    root: WARN
    com.videoplayer: INFO
    org.springframework: WARN
    org.hibernate: WARN

# 禁用管理端点
management:
  endpoints:
    enabled-by-default: false

# 应用配置
app:
  upload:
    path: /tmp/test-uploads
    max-file-size: 10485760 # 10MB for tests
  
  security:
    enable-xss-protection: false
    enable-sql-injection-protection: false
    enable-csrf-protection: false
    enable-rate-limiting: false
  
  cache:
    enabled: false
