<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light only">
    <meta name="theme-color" content="#ffffff">
    <meta name="msapplication-navbutton-color" content="#ffffff">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="supported-color-schemes" content="light">
    <title th:text="${pageTitle}">视频管理</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 防夜间模式CSS（最高优先级） -->
    <link href="/css/anti-dark-mode.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/admin-style.css" rel="stylesheet">
</head>
<body style="background-color: #ffffff !important; color: #333333 !important; color-scheme: light only !important;">
    <!-- 全局提示容器 -->
    <div class="alert-container" id="alertContainer"></div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="text-center text-white">
            <div class="spinner-border mb-3" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div>正在处理，请稍候...</div>
        </div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 快速操作 -->
                <div class="d-flex gap-1">
                    <a href="/admin/add" class="btn btn-success btn-sm">
                        <i class="fas fa-plus me-1"></i>添加
                    </a>
                    <button class="btn btn-outline-light btn-sm" onclick="refreshPage()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container-fluid px-4">
        <!-- 管理页面头部 -->
        <div class="admin-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="display-5 fw-bold mb-3">
                            <i class="fas fa-video me-3"></i>视频管理中心
                        </h1>
                        <p class="lead mb-0 opacity-90">轻松管理您的视频内容，支持批量操作和智能搜索</p>
                    </div>
                    <div class="col-lg-4 text-end">
                        <div class="d-flex justify-content-end gap-2">
                            <button class="btn btn-light btn-lg" onclick="showHelp()">
                                <i class="fas fa-question-circle me-2"></i>使用帮助
                            </button>
                            <button class="btn btn-warning btn-lg" onclick="exportData()">
                                <i class="fas fa-download me-2"></i>导出数据
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <!-- 统计卡片 -->
            <div class="row g-4 mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="card stat-card bg-primary text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title mb-2 opacity-90">总视频数</h6>
                                    <h2 class="mb-0 fw-bold" th:text="${videos != null ? videos.size() : 0}">0</h2>
                                    <small class="opacity-75">个视频文件</small>
                                </div>
                                <div class="text-end">
                                    <i class="fas fa-video fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card stat-card bg-success text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title mb-2 opacity-90">启用视频</h6>
                                    <h2 class="mb-0 fw-bold" id="activeCount">0</h2>
                                    <small class="opacity-75">正在展示</small>
                                </div>
                                <div class="text-end">
                                    <i class="fas fa-check-circle fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card stat-card bg-warning text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title mb-2 opacity-90">禁用视频</h6>
                                    <h2 class="mb-0 fw-bold" id="inactiveCount">0</h2>
                                    <small class="opacity-75">已隐藏</small>
                                </div>
                                <div class="text-end">
                                    <i class="fas fa-pause-circle fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card stat-card bg-info text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title mb-2 opacity-90">选中视频</h6>
                                    <h2 class="mb-0 fw-bold" id="selectedCount">0</h2>
                                    <small class="opacity-75">已选择</small>
                                </div>
                                <div class="text-end">
                                    <i class="fas fa-check-square fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <!-- 搜索和筛选区域 -->
            <div class="search-section">
                <div class="row g-3 align-items-end">
                    <div class="col-lg-4 col-md-6">
                        <label for="adminSearch" class="form-label fw-semibold">
                            <i class="fas fa-search me-1"></i>搜索视频
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="adminSearch"
                                   placeholder="输入标题或描述关键词..." th:value="${param.search}">
                            <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="form-text">支持模糊搜索，实时过滤结果</div>
                    </div>

                    <div class="col-lg-2 col-md-3">
                        <label for="statusFilter" class="form-label fw-semibold">状态</label>
                        <select class="form-select" id="statusFilter" th:value="${param.status}">
                            <option value="">全部状态</option>
                            <option value="active">✅ 启用中</option>
                            <option value="inactive">⏸️ 已禁用</option>
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-3">
                        <label for="formatFilter" class="form-label fw-semibold">格式</label>
                        <select class="form-select" id="formatFilter" th:value="${param.format}">
                            <option value="">全部格式</option>
                            <option value="mp4">MP4</option>
                            <option value="avi">AVI</option>
                            <option value="mov">MOV</option>
                            <option value="wmv">WMV</option>
                            <option value="flv">FLV</option>
                            <option value="webm">WebM</option>
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-6">
                        <label for="sortBy" class="form-label fw-semibold">排序</label>
                        <select class="form-select" id="sortBy">
                            <option value="createdTime">⏰ 创建时间</option>
                            <option value="title">📝 标题</option>
                            <option value="duration">⏱️ 时长</option>
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-6">
                        <div class="d-grid gap-2 d-md-flex">
                            <button class="btn btn-primary" onclick="applyFilters()">
                                <i class="fas fa-filter me-1"></i>筛选
                            </button>
                            <button class="btn btn-outline-secondary" onclick="resetFilters()">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <!-- 操作工具栏 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div class="d-flex align-items-center gap-3">
                    <h3 class="mb-0 fw-bold">
                        <i class="fas fa-list me-2 text-primary"></i>视频列表
                    </h3>
                    <span class="badge bg-secondary" id="totalVideosCount">
                        共 <span th:text="${videos != null ? videos.size() : 0}">0</span> 个视频
                    </span>
                    <span class="badge bg-info" id="filteredCount" style="display: none;">
                        筛选后 <span>0</span> 个
                    </span>
                </div>

                <div class="d-flex gap-2 flex-wrap">
                    <!-- 批量操作 -->
                    <div class="btn-group" role="group">
                        <input type="checkbox" class="btn-check" id="selectAll" autocomplete="off">
                        <label class="btn btn-outline-primary" for="selectAll">
                            <i class="fas fa-check-square me-1"></i>全选
                        </label>

                        <button class="btn btn-outline-success batch-operation" disabled onclick="batchEnable()" title="批量启用">
                            <i class="fas fa-play me-1"></i>启用
                        </button>
                        <button class="btn btn-outline-warning batch-operation" disabled onclick="batchDisable()" title="批量禁用">
                            <i class="fas fa-pause me-1"></i>禁用
                        </button>
                        <button class="btn btn-outline-danger batch-operation" disabled onclick="batchDelete()" title="批量删除">
                            <i class="fas fa-trash me-1"></i>删除
                        </button>
                    </div>

                    <!-- 快速操作 -->
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-info" onclick="importData()" title="导入数据">
                            <i class="fas fa-upload me-1"></i>导入
                        </button>
                        <button class="btn btn-outline-secondary" onclick="refreshPage()" title="刷新页面">
                            <i class="fas fa-sync-alt me-1"></i>刷新
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 视频列表 -->
        <div class="table-container">
            <!-- 空状态提示 -->
            <div th:if="${videos == null or videos.isEmpty()}" class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-video fa-4x text-muted"></i>
                </div>
                <h4 class="text-muted">暂无视频</h4>
                <p class="text-muted mb-4">还没有上传任何视频，点击下方按钮开始添加</p>
                <a href="/admin/add" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>添加第一个视频
                </a>
            </div>

            <!-- 视频表格 -->
            <div th:if="${videos != null and !videos.isEmpty()}">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="videosTable">
                        <thead class="table-light sticky-top">
                            <tr>
                                <th style="width: 50px;" class="text-center">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="selectAllTable">
                                        <label class="form-check-label" for="selectAllTable"></label>
                                    </div>
                                </th>
                                <th style="width: 80px;" class="text-center">ID</th>
                                <th style="width: 120px;" class="text-center">缩略图</th>
                                <th>视频信息</th>
                                <th style="width: 120px;" class="text-center">技术参数</th>
                                <th style="width: 120px;" class="text-center">创建时间</th>
                                <th style="width: 100px;" class="text-center">状态</th>
                                <th style="width: 200px;" class="text-center">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="video : ${videos}"
                                th:class="${video.isActive ? 'video-row' : 'video-row disabled'}"
                                th:data-video-id="${video.id}">

                                <!-- 选择框 -->
                                <td class="text-center">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input video-checkbox"
                                               th:value="${video.id}" th:id="'check-' + ${video.id}">
                                        <label class="form-check-label" th:for="'check-' + ${video.id}"></label>
                                    </div>
                                </td>

                                <!-- ID -->
                                <td class="text-center">
                                    <span class="badge bg-light text-dark fw-bold" th:text="${video.id}">1</span>
                                </td>

                                <!-- 缩略图 -->
                                <td class="text-center">
                                    <div class="position-relative">
                                        <img th:src="${video.thumbnailUrl != null ? video.thumbnailUrl : 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png'}"
                                             class="video-thumbnail shadow-sm thumbnail-optimized"
                                             th:alt="${video.title}"
                                             loading="lazy"
                                             decoding="async"
                                             onerror="this.src='https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png'"
                                             onload="this.classList.add('loaded')"
                                             data-loaded="false">
                                        <div class="position-absolute top-0 end-0 translate-middle">
                                            <span class="badge bg-dark bg-opacity-75 text-white"
                                                  th:if="${video.duration != null}"
                                                  th:text="${video.duration + 's'}">120s</span>
                                        </div>
                                    </div>
                                </td>

                                <!-- 视频信息 -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <h6 class="video-title mb-1" th:text="${video.title}">视频标题</h6>
                                        <p class="video-description mb-2"
                                           th:if="${video.description != null and !video.description.isEmpty()}"
                                           th:text="${video.description}">视频描述内容...</p>
                                        <div class="d-flex gap-2 flex-wrap">
                                            <span class="badge bg-primary bg-opacity-10 text-primary"
                                                  th:if="${video.videoUrl != null}">
                                                <i class="fas fa-link me-1"></i>有链接
                                            </span>
                                            <span class="badge bg-success bg-opacity-10 text-success"
                                                  th:if="${video.thumbnailUrl != null}">
                                                <i class="fas fa-image me-1"></i>有缩略图
                                            </span>
                                        </div>
                                    </div>
                                </td>

                                <!-- 技术参数 -->
                                <td class="text-center">
                                    <div class="d-flex flex-column gap-1">
                                        <span class="badge bg-info"
                                              th:text="${video.videoFormat != null ? video.videoFormat.toUpperCase() : 'N/A'}">MP4</span>
                                        <small class="text-muted"
                                               th:text="${video.resolution != null ? video.resolution : '未知分辨率'}">1080p</small>
                                    </div>
                                </td>

                                <!-- 创建时间 -->
                                <td class="text-center">
                                    <div class="d-flex flex-column">
                                        <span class="fw-semibold" th:text="${#temporals.format(video.createdTime, 'MM-dd')}">01-01</span>
                                        <small class="text-muted" th:text="${#temporals.format(video.createdTime, 'HH:mm')}">12:00</small>
                                    </div>
                                </td>

                                <!-- 状态 -->
                                <td class="text-center">
                                    <span class="status-badge badge bg-success" th:if="${video.isActive}">
                                        <i class="fas fa-check me-1"></i>启用
                                    </span>
                                    <span class="status-badge badge bg-secondary" th:unless="${video.isActive}">
                                        <i class="fas fa-pause me-1"></i>禁用
                                    </span>
                                </td>
                                <!-- 操作 -->
                                <td class="text-center">
                                    <div class="d-flex justify-content-center gap-1 flex-wrap">
                                        <!-- 主要操作 -->
                                        <a th:href="@{/play/{id}(id=${video.id})}"
                                           class="btn btn-sm btn-outline-primary action-btn"
                                           title="播放视频" target="_blank">
                                            <i class="fas fa-play"></i>
                                        </a>

                                        <a th:href="@{/admin/edit/{id}(id=${video.id})}"
                                           class="btn btn-sm btn-outline-warning action-btn"
                                           title="编辑视频">
                                            <i class="fas fa-edit"></i>
                                        </a>

                                        <button class="btn btn-sm btn-outline-info action-btn"
                                                th:onclick="'copyVideoUrl(' + ${video.id} + ')'"
                                                title="复制链接">
                                            <i class="fas fa-copy"></i>
                                        </button>

                                        <!-- 状态切换 -->
                                        <button class="btn btn-sm action-btn"
                                                th:class="${video.isActive ? 'btn-outline-warning' : 'btn-outline-success'}"
                                                th:onclick="'toggleVideoStatus(' + ${video.id} + ', ' + ${video.isActive} + ')'"
                                                th:title="${video.isActive ? '禁用视频' : '启用视频'}">
                                            <i th:class="${video.isActive ? 'fas fa-pause' : 'fas fa-play'}"></i>
                                        </button>

                                        <!-- 删除按钮 -->
                                        <button class="btn btn-sm btn-outline-danger action-btn"
                                                th:onclick="'confirmDeleteVideo(' + ${video.id} + ', \'' + ${video.title} + '\')"
                                                title="删除视频">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 表格底部信息 -->
                <div class="d-flex justify-content-between align-items-center p-3 bg-light border-top">
                    <div class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        共显示 <span class="fw-bold" th:text="${videos.size()}">0</span> 个视频
                        <span id="selectedInfo" class="ms-3" style="display: none;">
                            已选择 <span class="fw-bold text-primary">0</span> 个
                        </span>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="selectAll()">
                            <i class="fas fa-check-square me-1"></i>全选
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="selectNone()">
                            <i class="fas fa-square me-1"></i>取消
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="selectInvert()">
                            <i class="fas fa-exchange-alt me-1"></i>反选
                        </button>
                    </div>
                </div>
            </div>
        </div>


    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-play-circle me-2"></i>佳茵轻康视频</h5>
                    <p class="mb-0">专业的视频管理解决方案，轻康自然，享瘦生活。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>© 2025 佳茵轻康. <a href="/about" class="text-light text-decoration-none">关于我们</a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- 确认删除模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle me-2"></i>确认删除
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-trash fa-3x text-danger"></i>
                    </div>
                    <p class="text-center mb-3">您确定要删除这个视频吗？</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>注意：</strong>此操作不可撤销，删除后视频将无法恢复。
                    </div>
                    <div class="bg-light p-3 rounded">
                        <strong>视频标题：</strong><span id="deleteVideoTitle" class="text-primary"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash me-1"></i>确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助模态框 -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-question-circle me-2"></i>使用帮助
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-search me-2 text-primary"></i>搜索功能</h6>
                            <ul class="list-unstyled mb-4">
                                <li>• 支持标题和描述模糊搜索</li>
                                <li>• 实时过滤，无需点击搜索按钮</li>
                                <li>• 可按状态和格式筛选</li>
                            </ul>

                            <h6><i class="fas fa-check-square me-2 text-success"></i>批量操作</h6>
                            <ul class="list-unstyled">
                                <li>• 勾选视频后可批量启用/禁用</li>
                                <li>• 支持批量删除操作</li>
                                <li>• 全选/反选功能</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-cog me-2 text-warning"></i>视频管理</h6>
                            <ul class="list-unstyled mb-4">
                                <li>• 点击播放按钮预览视频</li>
                                <li>• 编辑按钮修改视频信息</li>
                                <li>• 复制按钮获取视频链接</li>
                                <li>• 状态切换启用/禁用视频</li>
                            </ul>

                            <h6><i class="fas fa-keyboard me-2 text-info"></i>快捷键</h6>
                            <ul class="list-unstyled">
                                <li>• <kbd>Ctrl+A</kbd>：全选视频</li>
                                <li>• <kbd>Delete</kbd>：删除选中视频</li>
                                <li>• <kbd>F5</kbd>：刷新页面</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                        <i class="fas fa-check me-1"></i>我知道了
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/thumbnail-simple.js"></script>
    <script src="/js/navbar-fix.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/admin.js"></script>
</body>
</html>

