/**
 * 防夜间模式专用样式文件
 * Anti Dark Mode Styles
 * 确保网站在任何情况下都不受系统夜间模式影响
 */

/* ========== 最高优先级防护 ========== */

/* 强制根元素使用亮色模式 */
:root {
    color-scheme: light only !important;
    --bs-body-bg: #ffffff !important;
    --bs-body-color: #333333 !important;
    --bs-primary: #0d6efd !important;
    --bs-secondary: #6c757d !important;
    --bs-success: #198754 !important;
    --bs-info: #0dcaf0 !important;
    --bs-warning: #ffc107 !important;
    --bs-danger: #dc3545 !important;
    --bs-light: #f8f9fa !important;
    --bs-dark: #212529 !important;
}

/* HTML和Body强制亮色 */
html {
    color-scheme: light only !important;
    background-color: #ffffff !important;
    color: #333333 !important;
}

body {
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
}

/* ========== 全局元素防护 ========== */

/* 所有元素强制继承亮色模式 */
*, *::before, *::after {
    color-scheme: light only !important;
    background-color: inherit !important;
    color: inherit !important;
}

/* 容器元素 */
div, section, article, main, header, footer, nav, aside, 
.container, .container-fluid, .row, .col, [class*="col-"] {
    background-color: transparent !important;
    color: inherit !important;
}

/* 文本元素 */
p, h1, h2, h3, h4, h5, h6, span, a, label, small, strong, em, b, i {
    color: inherit !important;
    background-color: transparent !important;
}

/* 列表元素 */
ul, ol, li, dl, dt, dd {
    color: inherit !important;
    background-color: transparent !important;
}

/* 表格元素 */
table, thead, tbody, tfoot, tr, th, td {
    background-color: inherit !important;
    color: inherit !important;
}

/* 表单元素 */
form, fieldset, legend, input, textarea, select, button {
    background-color: inherit !important;
    color: inherit !important;
}

/* ========== Bootstrap组件防护 ========== */

/* 导航栏 */
.navbar {
    color-scheme: light only !important;
}

.navbar-dark {
    --bs-navbar-color: rgba(255,255,255,.55) !important;
    --bs-navbar-hover-color: rgba(255,255,255,.75) !important;
    --bs-navbar-active-color: #fff !important;
}

/* 卡片 */
.card {
    background-color: #ffffff !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* 按钮 */
.btn {
    color-scheme: light only !important;
}

/* 表单控件 */
.form-control, .form-select {
    background-color: #ffffff !important;
    color: #333333 !important;
    border-color: #ced4da !important;
}

/* 模态框 */
.modal-content {
    background-color: #ffffff !important;
    color: #333333 !important;
}

/* 下拉菜单 */
.dropdown-menu {
    background-color: #ffffff !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* 警告框 */
.alert {
    color-scheme: light only !important;
}

/* 表格 */
.table {
    color: #333333 !important;
}

.table th {
    background-color: #f8f9fa !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

.table td {
    background-color: #ffffff !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* ========== 媒体查询防护 ========== */

/* 覆盖深色模式偏好 */
@media (prefers-color-scheme: dark) {
    :root {
        color-scheme: light only !important;
    }
    
    html, body {
        background-color: #ffffff !important;
        color: #333333 !important;
        color-scheme: light only !important;
    }
    
    * {
        color-scheme: light only !important;
    }
}

/* 移动端防护 */
@media screen and (max-width: 768px) {
    html, body {
        background-color: #ffffff !important;
        color: #333333 !important;
        color-scheme: light only !important;
    }
}

/* 平板端防护 */
@media screen and (min-width: 769px) and (max-width: 1024px) {
    html, body {
        background-color: #ffffff !important;
        color: #333333 !important;
        color-scheme: light only !important;
    }
}

/* ========== 特殊情况防护 ========== */

/* 防止任何深色主题类 */
.dark, .dark-mode, .dark-theme, .theme-dark,
[data-theme="dark"], [data-bs-theme="dark"] {
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
}

/* 防止系统级深色模式 */
@media screen {
    html {
        color-scheme: light only !important;
        background-color: #ffffff !important;
    }
}

/* 强制所有可能的选择器 */
html[data-theme], body[data-theme],
html[class*="dark"], body[class*="dark"],
html[class*="theme"], body[class*="theme"] {
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
}

/* ========== 视频播放器特殊处理 ========== */

/* 视频播放器容器保持黑色背景 */
.video-player-container, #video-player, video {
    background-color: #000000 !important;
    color-scheme: light only !important;
}

/* 视频控制栏 */
.video-js .vjs-control-bar {
    background-color: rgba(0,0,0,0.7) !important;
}

/* ========== 最终保险 ========== */

/* 使用最高优先级确保样式生效 */
html, body {
    background: #ffffff !important;
    color: #333 !important;
}

/* 防止任何可能的覆盖 */
* {
    color-scheme: light only !important;
}
