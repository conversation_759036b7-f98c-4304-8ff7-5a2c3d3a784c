version: '3.8'

services:
  # 应用服务
  video-player:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: video-player-app
    ports:
      - "5000:5000"
    environment:
      - SPRING_DATASOURCE_URL=*****************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=root
      - SPRING_DATASOURCE_PASSWORD=root123456
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      - mysql
    networks:
      - video-player-network
    restart: unless-stopped

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: video-player-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=root123456
      - MYSQL_DATABASE=video_player
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - video-player-network
    restart: unless-stopped

# 网络配置
networks:
  video-player-network:
    driver: bridge

# 数据卷配置
volumes:
  mysql_data:
    driver: local
