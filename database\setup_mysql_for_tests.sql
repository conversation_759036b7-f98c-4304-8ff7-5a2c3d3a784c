-- MySQL数据库和用户设置脚本
-- 用于解决测试环境的数据库连接问题

-- 创建数据库
CREATE DATABASE IF NOT EXISTS video_player CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS video_player_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'video_player'@'localhost' IDENTIFIED BY 'root';
CREATE USER IF NOT EXISTS 'root'@'localhost' IDENTIFIED BY 'root';

-- 授予权限
GRANT ALL PRIVILEGES ON video_player.* TO 'video_player'@'localhost';
GRANT ALL PRIVILEGES ON video_player_test.* TO 'video_player'@'localhost';
GRANT ALL PRIVILEGES ON video_player.* TO 'root'@'localhost';
GRANT ALL PRIVILEGES ON video_player_test.* TO 'root'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示用户和权限
SELECT User, Host FROM mysql.user WHERE User IN ('video_player', 'root');
SHOW GRANTS FOR 'video_player'@'localhost';
SHOW GRANTS FOR 'root'@'localhost';
